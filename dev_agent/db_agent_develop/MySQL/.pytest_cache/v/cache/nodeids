["test_mysql_server_schemas.py::TestMySQLAgentInputSchema::test_empty_query_validation_error", "test_mysql_server_schemas.py::TestMySQLAgentInputSchema::test_missing_query_validation_error", "test_mysql_server_schemas.py::TestMySQLAgentInputSchema::test_multiline_query_schema", "test_mysql_server_schemas.py::TestMySQLAgentInputSchema::test_query_with_special_characters", "test_mysql_server_schemas.py::TestMySQLAgentInputSchema::test_valid_complex_query", "test_mysql_server_schemas.py::TestMySQLAgentInputSchema::test_valid_describe_query", "test_mysql_server_schemas.py::TestMySQLAgentInputSchema::test_valid_select_query", "test_mysql_server_schemas.py::TestMySQLAgentInputSchema::test_valid_show_tables_query", "test_mysql_server_schemas.py::TestMySQLAgentOutputSchema::test_affected_rows_output", "test_mysql_server_schemas.py::TestMySQLAgentOutputSchema::test_complex_result_structure", "test_mysql_server_schemas.py::TestMySQLAgentOutputSchema::test_empty_result_output", "test_mysql_server_schemas.py::TestMySQLAgentOutputSchema::test_error_message_output", "test_mysql_server_schemas.py::TestMySQLAgentOutputSchema::test_select_result_output", "test_mysql_server_schemas.py::TestMySQLAgentOutputSchema::test_single_row_result", "test_mysql_server_schemas.py::TestMySQLAgentToolRealDatabase::test_database_connection", "test_mysql_server_schemas.py::TestMySQLAgentToolRealDatabase::test_describe_information_schema", "test_mysql_server_schemas.py::TestMySQLAgentToolRealDatabase::test_invalid_sql_error_handling", "test_mysql_server_schemas.py::TestMySQLAgentToolRealDatabase::test_nonexistent_table_error", "test_mysql_server_schemas.py::TestMySQLAgentToolRealDatabase::test_select_from_information_schema", "test_mysql_server_schemas.py::TestMySQLAgentToolRealDatabase::test_show_tables_execution", "test_mysql_server_schemas.py::TestSchemaSerializationRoundtrip::test_mysql_input_json_roundtrip", "test_mysql_server_schemas.py::TestSchemaSerializationRoundtrip::test_mysql_output_json_roundtrip", "test_mysql_server_schemas.py::TestSchemaSerializationRoundtrip::test_tool_response_json_roundtrip", "test_mysql_server_schemas.py::TestToolResponseSchema::test_tool_content_with_complex_data", "test_mysql_server_schemas.py::TestToolResponseSchema::test_tool_response_from_model", "test_mysql_server_schemas.py::TestToolResponseSchema::test_tool_response_from_text"]