Formulate and restructure the prompt in `/merge/mcp-agent/examples/usecases/mcp_financial_analyzer/agents/mysql_agent.py` to follow the exact pattern and structure used in `/merge/dev_agent/db_agent_develop/MySQL/client.py`. Specifically:

1. Update the prompt formatting and structure to match the reference implementation
2. Align the input schema to follow the same pattern as the reference (likely using MCPOrchestratorInputSchema or similar)
3. Ensure the output schema follows the same conventions and structure as the reference implementation
4. Maintain consistency in prompt engineering techniques, parameter definitions, and schema validation patterns between both files

The goal is to standardize the MySQL agent implementation to match the established patterns in the reference client implementation.